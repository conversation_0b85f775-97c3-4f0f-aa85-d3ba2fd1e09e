import React, { useState, useEffect } from "react";
import "./styles/coverpage.css";
import { Link, useLocation } from "react-router-dom";
import INFO from "../../data/user";

const CoverPage = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const location = useLocation();

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <div className="cover-page-container">
      <div className="cover-wave"></div>

      {/* Cursor follower effect */}
      <div
        className="cursor-follower"
        style={{
          left: mousePosition.x - 10,
          top: mousePosition.y - 10,
        }}
      ></div>

      <div className="cover-navigation">
        <div className="cover-nav-links">
          <Link to="/" className={isActive("/") ? "active" : ""}>Home</Link>
          <Link to="/about" className={isActive("/about") ? "active" : ""}>About</Link>
          <Link to="/projects" className={isActive("/projects") ? "active" : ""}>Projects</Link>
          <Link to="/articles" className={isActive("/articles") ? "active" : ""}>Articles</Link>
          <Link to="/contact" className={isActive("/contact") ? "active" : ""}>Contact</Link>
        </div>
      </div>

      <div className="cover-avatar-container">
        <div className="cover-avatar">
          <img
            src={INFO.main.logo}
            alt="Avatar"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = "https://via.placeholder.com/150/ffdd00/000000?text=HG";
            }}
          />
        </div>
      </div>

      <div className="cover-divider"></div>
    </div>
  );
};

export default CoverPage;
