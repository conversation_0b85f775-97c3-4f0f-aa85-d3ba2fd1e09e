@import "../../../data/styles.css";

.nav-container {
	margin: 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

.navbar {
	display: flex;
	justify-content: center;
	align-items: center;
	position: fixed;
	top: 3vh;
	z-index: 998;
}

.nav-background {
	width: 380px;
	height: 40px;
	padding-left: 0%;
	padding-right: 0%;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(15px);
	box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3);
	border-radius: 40px;
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-list {
	display: flex;
	justify-content: space-between;
	list-style: none;
	align-items: center;
	margin-left: -16px;
	margin-right: 20px;
	margin-top: 11px;
}

.nav-item {
	font-weight: bold;
	font-size: 80%;
}

.nav-item.active a {
	color: #ffd700 !important;
	background: rgba(255, 215, 0, 0.25) !important;
	text-shadow: 0 0 10px rgba(255, 215, 0, 0.7) !important;
	font-weight: 700;
}

.nav-item a {
	text-decoration: none;
	color: #fff;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
	transition: all 0.3s ease;
	padding: 4px 8px;
	border-radius: 15px;
}

.nav-item a:hover {
	color: #ffd700;
	background: rgba(255, 215, 0, 0.2);
	text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
	transform: scale(1.05);
	transition: all 0.3s ease-in-out;
}

@media (max-width: 600px) {
	.navbar {
		margin-left: 25%;
		margin-right: 25%;
		width: 80%;
		font-size: 80%;
	}

	.nav-background {
		width: 80%;
		height: 40px;
	}

	.nav-item {
		font-weight: bold;
	}
}
