.cover-page-container {
  position: relative;
  width: 100%;
  height: 280px;
  margin-bottom: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #ffdd00 0%, #ffc107 30%, #ff9800 70%, #ffdd00 100%);
  animation: gradientShift 8s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background: linear-gradient(135deg, #ffdd00 0%, #ffc107 30%, #ff9800 70%, #ffdd00 100%);
  }
  50% {
    background: linear-gradient(135deg, #ffc107 0%, #ffdd00 30%, #ffc107 70%, #ff9800 100%);
  }
}

.cover-wave {
  position: absolute;
  top: 0;
  left: 0;
  width: 120%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffdd00' fill-opacity='0.8' d='M0,160L60,170.7C120,181,240,203,360,192C480,181,600,139,720,128C840,117,960,139,1080,149.3C1200,160,1320,160,1380,160L1440,160L1440,0L1380,0C1320,0,1200,0,1080,0C960,0,840,0,720,0C600,0,480,0,360,0C240,0,120,0,60,0L0,0Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-position: bottom;
  z-index: 1;
  animation: waveMove 6s ease-in-out infinite;
}

@keyframes waveMove {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(-30px);
  }
}

.cover-navigation {
  display: flex;
  justify-content: center;
  padding-top: 30px;
  z-index: 1000;
  position: relative;
}

.cover-nav-links {
  display: flex;
  gap: 30px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px 35px;
  border-radius: 35px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.cover-nav-links:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.cover-nav-links a {
  text-decoration: none;
  color: #333;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  padding: 5px 10px;
  border-radius: 20px;
  position: relative;
}

.cover-nav-links a:hover {
  color: #ff9800;
  background: rgba(255, 152, 0, 0.1);
  transform: scale(1.05);
}

.cover-nav-links a.active {
  color: #ff9800;
  background: rgba(255, 152, 0, 0.15);
  font-weight: 700;
}

/* Cursor follower effect */
.cursor-follower {
  position: fixed;
  width: 20px;
  height: 20px;
  background: rgba(255, 152, 0, 0.6);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: all 0.1s ease;
  mix-blend-mode: difference;
}

.cover-avatar-container {
  position: absolute;
  bottom: 0;
  left: 80px;
  transform: translateY(50%);
  z-index: 15;
  animation: avatarFloat 4s ease-in-out infinite;
}

@keyframes avatarFloat {
  0%, 100% {
    transform: translateY(50%);
  }
  50% {
    transform: translateY(45%);
  }
}

.cover-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  background: white;
}

.cover-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.cover-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #000 0%, #333 50%, #000 100%);
  z-index: 20;
}

/* Floating decorative elements */
.cover-page-container::before {
  content: '';
  position: absolute;
  top: 15%;
  right: 15%;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: float1 5s ease-in-out infinite;
  z-index: 2;
}

.cover-page-container::after {
  content: '';
  position: absolute;
  top: 70%;
  right: 8%;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: float2 4s ease-in-out infinite reverse;
  z-index: 2;
}

@keyframes float1 {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
  }
}

@keyframes float2 {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-15px) scale(1.1);
  }
}

@media (max-width: 768px) {
  .cover-page-container {
    height: 220px;
  }

  .cover-navigation {
    padding-top: 30px;
  }

  .cover-nav-links {
    gap: 20px;
    padding: 10px 25px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .cover-nav-links a {
    font-size: 14px;
    padding: 4px 8px;
  }

  .cover-avatar-container {
    left: 40px;
  }

  .cover-avatar {
    width: 100px;
    height: 100px;
    border: 4px solid white;
  }

  .cover-page-container::before {
    width: 60px;
    height: 60px;
    top: 20%;
    right: 10%;
  }

  .cover-page-container::after {
    width: 40px;
    height: 40px;
    top: 65%;
    right: 5%;
  }
}

@media (max-width: 480px) {
  .cover-page-container {
    height: 200px;
  }

  .cover-navigation {
    padding-top: 25px;
  }

  .cover-nav-links {
    gap: 12px;
    padding: 8px 20px;
    flex-direction: column;
    align-items: center;
  }

  .cover-nav-links a {
    font-size: 13px;
    padding: 3px 6px;
  }

  .cover-avatar-container {
    left: 30px;
  }

  .cover-avatar {
    width: 80px;
    height: 80px;
    border: 3px solid white;
  }

  .cover-page-container::before,
  .cover-page-container::after {
    display: none;
  }
}
