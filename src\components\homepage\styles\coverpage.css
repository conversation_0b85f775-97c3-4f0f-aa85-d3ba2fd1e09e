.cover-page-container {
  position: relative;
  width: 100%;
  height: 280px;
  margin-bottom: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #0c0c1e 0%, #1a1a3e 30%, #2d1b69 70%, #0c0c1e 100%);
  animation: spaceShift 12s ease-in-out infinite;
}

@keyframes spaceShift {
  0%, 100% {
    background: linear-gradient(135deg, #0c0c1e 0%, #1a1a3e 30%, #2d1b69 70%, #0c0c1e 100%);
  }
  33% {
    background: linear-gradient(135deg, #1a1a3e 0%, #2d1b69 30%, #4a148c 70%, #1a1a3e 100%);
  }
  66% {
    background: linear-gradient(135deg, #2d1b69 0%, #4a148c 30%, #6a1b9a 70%, #2d1b69 100%);
  }
}

/* Space background elements */
.space-stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #fff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #fff, transparent),
    radial-gradient(1px 1px at 180px 10px, rgba(255,215,0,0.8), transparent),
    radial-gradient(1px 1px at 60px 90px, rgba(79,195,247,0.7), transparent),
    radial-gradient(2px 2px at 120px 50px, rgba(255,107,107,0.6), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: twinkle 4s ease-in-out infinite alternate;
  z-index: 1;
}

.space-planets {
  position: absolute;
  top: 10%;
  right: 10%;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle at 30% 30%, #ff6b6b, #8e24aa);
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
  animation: planetFloat 8s ease-in-out infinite;
  z-index: 2;
}

.space-planets::before {
  content: '';
  position: absolute;
  top: 60%;
  right: -40px;
  width: 30px;
  height: 30px;
  background: radial-gradient(circle at 40% 40%, #4fc3f7, #1976d2);
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(79, 195, 247, 0.4);
  animation: planetFloat 6s ease-in-out infinite reverse;
}

.space-nebula {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse at 20% 50%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(79, 195, 247, 0.1) 0%, transparent 50%);
  animation: nebulaShift 15s ease-in-out infinite;
  z-index: 1;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes planetFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

@keyframes nebulaShift {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* Space Rocket */
.space-rocket {
  position: fixed;
  width: 30px;
  height: 30px;
  font-size: 24px;
  pointer-events: none;
  z-index: 9999;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
  transform-origin: center;
  animation: rocketPulse 2s ease-in-out infinite;
}

@keyframes rocketPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.space-rocket::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -20px;
  width: 15px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.8), transparent);
  border-radius: 2px;
  animation: rocketTrail 0.5s ease-in-out infinite;
}

@keyframes rocketTrail {
  0%, 100% { opacity: 0.3; transform: scaleX(0.5); }
  50% { opacity: 0.8; transform: scaleX(1); }
}

.space-rocket.docked {
  animation: rocketDock 0.5s ease-in-out, rocketDockedPulse 1s ease-in-out infinite;
  transform: scale(1.2);
  filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.8)) drop-shadow(0 0 30px rgba(255, 215, 0, 0.4));
}

@keyframes rocketDockedPulse {
  0%, 100% { transform: scale(1.2); }
  50% { transform: scale(1.3); }
}

.rocket-dock-label {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  border: 1px solid rgba(255, 215, 0, 0.5);
}

@keyframes rocketDock {
  0% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.3) rotate(10deg); }
  100% { transform: scale(1.2) rotate(0deg); }
}

.cover-navigation {
  display: flex;
  justify-content: center;
  padding-top: 30px;
  z-index: 1000;
  position: relative;
}

.cover-nav-links {
  display: flex;
  gap: 30px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 35px;
  border-radius: 35px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.cover-nav-links:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
  background: rgba(255, 255, 255, 0.15);
}

.cover-nav-links a {
  text-decoration: none;
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  padding: 5px 10px;
  border-radius: 20px;
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.cover-nav-links a:hover {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
  transform: scale(1.05);
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.cover-nav-links a.active {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.25);
  font-weight: 700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.7);
}



.cover-avatar-container {
  position: absolute;
  bottom: 0;
  left: 80px;
  transform: translateY(50%);
  z-index: 15;
  animation: avatarFloat 4s ease-in-out infinite;
}

@keyframes avatarFloat {
  0%, 100% {
    transform: translateY(50%);
  }
  50% {
    transform: translateY(45%);
  }
}

.cover-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  background: white;
}

.cover-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.cover-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #000 0%, #333 50%, #000 100%);
  z-index: 20;
}

/* Floating space elements */
.cover-page-container::before {
  content: '🌟';
  position: absolute;
  top: 15%;
  right: 15%;
  width: 30px;
  height: 30px;
  font-size: 24px;
  animation: starFloat 5s ease-in-out infinite;
  z-index: 2;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.cover-page-container::after {
  content: '🛸';
  position: absolute;
  top: 70%;
  right: 8%;
  width: 40px;
  height: 40px;
  font-size: 32px;
  animation: ufoFloat 6s ease-in-out infinite reverse;
  z-index: 2;
  filter: drop-shadow(0 0 15px rgba(79, 195, 247, 0.6));
}

@keyframes starFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-25px) rotate(180deg) scale(1.2);
  }
}

@keyframes ufoFloat {
  0%, 100% {
    transform: translateY(0px) scale(1) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) scale(1.1) rotate(10deg);
  }
}

@media (max-width: 768px) {
  .cover-page-container {
    height: 220px;
  }

  .cover-navigation {
    padding-top: 30px;
  }

  .cover-nav-links {
    gap: 20px;
    padding: 10px 25px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .cover-nav-links a {
    font-size: 14px;
    padding: 4px 8px;
  }

  .cover-avatar-container {
    left: 40px;
  }

  .cover-avatar {
    width: 100px;
    height: 100px;
    border: 4px solid white;
  }

  .cover-page-container::before {
    font-size: 20px;
    top: 20%;
    right: 10%;
  }

  .cover-page-container::after {
    font-size: 28px;
    top: 65%;
    right: 5%;
  }
}

@media (max-width: 480px) {
  .cover-page-container {
    height: 200px;
  }

  .cover-navigation {
    padding-top: 25px;
  }

  .cover-nav-links {
    gap: 12px;
    padding: 8px 20px;
    flex-direction: column;
    align-items: center;
  }

  .cover-nav-links a {
    font-size: 13px;
    padding: 3px 6px;
  }

  .cover-avatar-container {
    left: 30px;
  }

  .cover-avatar {
    width: 80px;
    height: 80px;
    border: 3px solid white;
  }

  .cover-page-container::before,
  .cover-page-container::after {
    display: none;
  }
}
