@import "../../../data/styles.css";

.card {
	mix-blend-mode: normal;
	border-radius: 20px;
	outline: 2px solid rgba(255, 255, 255, 0.1);
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(10px);
}

.card-container {
	padding: 30px;
	padding-bottom: 5px;
}

.card-header {
	display: flex;
	align-items: center;
}

.card-icon {
	font-size: 15px;
	color: rgba(255, 255, 255, 0.7);
}

.card-title {
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	padding-left: 20px;
	font-weight: 650;
}

.card-body {
	padding-top: 40px;
	font-size: 15px;
}
