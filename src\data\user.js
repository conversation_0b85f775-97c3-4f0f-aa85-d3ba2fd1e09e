const INFO = {
	main: {
		title: "<PERSON><PERSON><PERSON><PERSON> | Portfolio",
		name: "<PERSON><PERSON><PERSON><PERSON>",
		email: "<EMAIL>",
		logo: "../logo.png",
	},

	socials: {
		github: "https://github.com/Harshith1201",
		linkedin: "https://www.linkedin.com/in/harshith-gangisetty-10071524a/",
		instagram: "https://instagram.com/",
		facebook: "https://facebook.com/",
	},

	homepage: {
		title: "Computer Science student specializing in Cyber Security with a keen interest in AI and network automation.",
		description:
			"I'm a Computer Science student at Amrita Vishwa Vidyapeetham with a focus on Cybersecurity. I have experience in Selenium-based automation, cybersecurity fundamentals, and tech writing. I'm passionate about leveraging emerging technologies to solve real-world problems and continuously expanding my knowledge in the field of cybersecurity and AI.",
	},

	about: {
		title: "I'm <PERSON><PERSON><PERSON><PERSON>, a Computer Science student from Coimbatore, Tamil Nadu.",
		description:
			"I'm currently pursuing my B.Tech in Computer Science Engineering with a concentration in Cybersecurity at Amrita Vishwa Vidyapeetham, expected to graduate in May 2026. With a CGPA of 7.89/10.0, I've focused on coursework including Object-Oriented Programming, Data Structures & Algorithms, Operating Systems, Computer Organization & Architecture, Database Management, and Cryptography. I'm passionate about cybersecurity, AI, and using technology to solve real-world problems. I actively participate in workshops, competitions, and have earned several certifications in areas like networking, prompt engineering, and AI.",
	},

	articles: {
		title: "Sharing insights and knowledge about cybersecurity, AI, and technology.",
		description:
			"A collection of my thoughts, tutorials, and analyses on topics related to cybersecurity, artificial intelligence, and emerging technologies.",
	},

	projects: [
		{
			title: "Malware Threat Intelligence Scrapper",
			description:
				"I built this real-time threat intelligence system out of my passion for cybersecurity. It automatically collects and analyzes malware, vulnerability, and phishing data from various online sources. I'm particularly proud of implementing the NLP components that help analyze threats and validate community reports. The web dashboard makes it easy for users to access actionable insights through a clean API.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/python/python.png",
			linkText: "View Project",
			link: "https://github.com/Harshith1201/malware-threat-intelligence-scrapper",
		},

		{
			title: "IOT-based Blood Bank Management System",
			description:
				"Working with a small team of three, I helped create this innovative blood bank management solution that I'm really excited about. We developed two React Native apps and a website that connect donors with recipients, while using IoT sensors to monitor blood storage conditions. I implemented the QR code-based safety verification system and worked on the real-time tracking features. This project taught me a lot about healthcare tech and the importance of reliable systems when lives are at stake.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/javascript/javascript.png",
			linkText: "View Project",
			link: "https://github.com/Harshith1201/blood-bank-management",
		},

		{
			title: "Human Factors Influencing Password Choices",
			description:
				"I'm currently conducting an ongoing research study on how people choose their passwords in Tamil Nadu. This project is close to my heart as it combines psychology with cybersecurity. We're gathering data through surveys to understand password habits and preferences. I'd love your contribution to this research! Please consider taking our short survey to help us develop better password policies that balance security with usability.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/python/python.png",
			linkText: "Take the Survey",
			link: "https://docs.google.com/forms/d/e/1FAIpQLSfYvVswr5dP1eylXbjS0-G_oZ9j5aDq5iXJxZDlJcN35C_PDA/viewform",
		},

		{
			title: "Implementation of Schnorr Signature",
			description:
				"During my cryptography coursework, I became fascinated with Schnorr signatures and their applications in blockchain technology. I created this interactive web-based simulation to help others understand how these digital signatures work. The project features a visual demonstration of signature aggregation and verification, making complex cryptographic concepts more accessible. I built this using Flask for the backend and simple HTML/CSS for the frontend interface.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/html/html.png",
			linkText: "View Project",
			link: "https://github.com/Harshith1201/schnorr-signature-implementation",
		},

		{
			title: "CyberReach – Cybersecurity Awareness Platform",
			description:
				"CyberReach is a project I'm deeply involved with as a content curator. We created this platform to make cybersecurity knowledge accessible to everyone. I developed a custom Python scraper using BeautifulSoup and Scrapy that automatically collects and summarizes the latest cybersecurity news. I also write and edit educational content about security best practices, emerging threats, and privacy tips. Our goal is to help people protect themselves online through engaging, easy-to-understand resources.",
			logo: "./cybereach.png",
			linkText: "View Project",
			link: "https://github.com/Harshith1201/cybereach-platform",
		},
	],
};

export default INFO;
