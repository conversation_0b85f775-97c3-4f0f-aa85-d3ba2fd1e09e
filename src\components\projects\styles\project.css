@import "../../../data/styles.css";

.project {
	mix-blend-mode: normal;
	border-radius: 20px;
	opacity: 0.8;
	height: 100%;
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.project a {
	text-decoration: none;
}

.project:hover {
	background: rgba(255, 255, 255, 0.1);
	opacity: 1;
	transition: background-color 0.3s ease-in-out;
	transform: translateY(-5px);
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.project:hover .project-link {
	color: #ffd700;
	transition: color 0.3s ease-in-out;
}

.project-container {
	padding: 30px;
}

.project-logo {
	width: 30px;
}

.project-logo img {
	width: 100%;
}

.project-title {
	font-family: var(--secondary-font);
	color: #fff;
	font-size: 16px;
	padding-top: 15px;
	margin-bottom: 1em;
	font-weight: 700;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.project-description {
	color: rgba(255, 255, 255, 0.8);
}

.project-link {
	display: flex;
	align-items: center;
	color: var(--secondary-color);
	font-size: 12px;
	padding-top: 20px;
}

.project-link-icon {
	padding-left: 5px;
	font-size: 13px;
}

.project-link-text {
	padding-left: 20px;
	font-weight: 700;
}

@media (max-width: 600px) {
	.project {
		height: 300px;
	}
}
