{"name": "reactfolio", "version": "1.1.0", "description": "Personal portfolio template built using Reactjs", "author": {"name": "Tharindu N<PERSON>", "email": "<EMAIL>", "url": "https://tharindu.dev"}, "license": "MIT", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-helmet": "^6.1.0", "react-router-dom": "^6.11.1", "react-scripts": "5.0.1", "styled-components": "^5.3.10", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "prettier": {"useTabs": true, "tabWidth": 4}}