.contact-logo-container {
	display: flex;
	justify-content: left;
	padding-top: 25px;
}

.contact-logo {
	display: flex;
	position: fixed;
	border: 1px solid white;
	border-radius: 50%;
	box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25);
	top: 4vh;
}

.contact-subtitle {
	width: 100% !important;
}

.contact-main-wrapper {
	display: flex;
	flex-direction: row;
	margin-top: 120px;
	gap: 30px;
}

.contact-left-column {
	flex: 2;
}

.contact-right-column {
	flex: 1;
	margin-top: 0;
}

.contact-container {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
}

.socials-container {
	display: flex;
	flex-direction: column;
	margin-top: 30px;
}

.contact-socials {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}

.game-wrapper {
	margin-top: 0;
}

/* Responsive layout for mobile */
@media (max-width: 768px) {
	.contact-main-wrapper {
		flex-direction: column;
	}

	.contact-right-column {
		margin-top: 30px;
	}
}
