@import "/src/data/styles.css";

::-webkit-scrollbar {
	width: 10px;
}

::-webkit-scrollbar-track {
	background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
	background-color: #383636;
	border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
	background-color: rgb(0, 0, 0);
}

.page-content {
	background: linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 50%, #16213e 100%);
	position: relative;
	min-height: 100vh;
}

.page-content:before,
.page-content:after {
	content: "";
	position: absolute;
	top: 0;
	bottom: 0;
	width: calc((100% - 1200px) / 2);
	background: linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 50%, #16213e 100%);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.page-content:before {
	left: 0;
	z-index: 1;
}

.page-content:after {
	right: 0;
	z-index: 1;
}

.content-wrapper {
	max-width: 1000px;
	margin: 0 auto;
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(10px);
	border-radius: 20px 20px 0 0;
	border: 1px solid rgba(255, 255, 255, 0.1);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	position: relative;
	z-index: 2;
	overflow: hidden;
}

.content-wrapper::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image:
		radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
		radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.05), transparent),
		radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.08), transparent),
		radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.03), transparent);
	background-repeat: repeat;
	background-size: 150px 80px;
	pointer-events: none;
	z-index: -1;
}

.title {
	color: #fff;
	font-family: var(--secondary-font);
	font-size: 45px;
	font-weight: 700;
	width: 70%;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
	padding-top: 10px;
	color: rgba(255, 255, 255, 0.8);
	font-size: 16px;
	line-height: 28px;
	width: 70%;
	margin-top: 25px;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.page-footer {
	position: block;
}

.page-footer::before {
	content: "";
	position: absolute;
	left: 0;
	right: 0;
	border-top: 2px solid rgba(255, 255, 255, 0.2);
}

@media (max-width: 1270px) {
	.page-content:before,
	.page-content:after {
		width: calc((100% - 90%) / 2);
	}

	.content-wrapper {
		max-width: 90%;
		padding-left: 10%;
		padding-right: 10%;
	}
}

@media (max-width: 1024px) {
	.page-content:before,
	.page-content:after {
		width: calc((100% - 950px) / 2);
	}

	.content-wrapper {
		max-width: 980px;
	}
}

@media (max-width: 800px) {
	.content-wrapper {
		max-width: 90%;
		margin: 0 auto;
		padding: 0 10px;
	}

	.page-content:before,
	.page-content:after {
		display: none;
	}

	.title {
		width: 100%;
	}

	.subtitle {
		width: 100%;
	}
}
